<?php
require_once '../bootstrap.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!verify_csrf_token($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
    exit;
}

$videoId = intval($input['video_id'] ?? 0);
if (!$videoId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid video ID']);
    exit;
}

$video = new Video();
$ipAddress = getClientIP();

// Check if video exists
$videoData = $video->getById($videoId);
if (!$videoData) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Video not found']);
    exit;
}

// Check if user already liked this video
$hasLiked = $video->hasUserLiked($videoId, $ipAddress);

try {
    if ($hasLiked) {
        // Remove like
        $result = $video->removeLike($videoId, $ipAddress);
        $liked = false;
    } else {
        // Add like
        $result = $video->addLike($videoId, $ipAddress);
        $liked = true;
    }
    
    if ($result) {
        // Get updated video data
        $updatedVideo = $video->getById($videoId);
        
        echo json_encode([
            'success' => true,
            'liked' => $liked,
            'likes_count' => intval($updatedVideo['likes_count'])
        ]);
    } else {
        throw new Exception('Failed to update like status');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
