# StoryGue .htaccess Configuration

# Enable URL Rewriting
RewriteEngine On

# Hide sensitive files
<Files ".env">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

<Files ".htaccess">
    Require all denied
</Files>

# Protect src directory
RewriteRule ^src/ - [F,L]

# Custom Error Pages (optional)
# ErrorDocument 404 /storygue/404.php

# Basic security headers (if mod_headers is available)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
</IfModule>

# Prevent execution of PHP in upload directories
<IfModule mod_rewrite.c>
    RewriteRule ^uploads/.*\.php$ - [F,L]
</IfModule>
