# StoryGue .htaccess Configuration

# Enable URL Rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' cdn.tailwindcss.com cdn-uicons.flaticon.com; img-src 'self' data:; font-src 'self' cdn-uicons.flaticon.com;"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.env$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>

# Protect config and src directories
<IfModule mod_rewrite.c>
    RewriteRule ^(config|src)/ - [F,L]
</IfModule>

# Custom Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-component "access plus 1 month"
    ExpiresByType video/mp4 "access plus 1 week"
    ExpiresByType video/webm "access plus 1 week"
</IfModule>

# File Upload Limits
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300

# URL Rewriting Rules
<IfModule mod_rewrite.c>
    # Remove .php extension from URLs
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.php [NC,L]
    
    # Redirect .php extension to clean URLs
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteRule ^ /%1 [NC,L,R=301]
    
    # Handle admin routes
    RewriteRule ^admin$ admin/index.php [NC,L]
    RewriteRule ^admin/(.*)$ admin/$1.php [NC,L]
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent execution of PHP in upload directories
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>
