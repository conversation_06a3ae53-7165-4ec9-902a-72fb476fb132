<?php
// Application configuration
return [
    'name' => 'StoryGue',
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'debug' => $_ENV['APP_DEBUG'] ?? true,
    'timezone' => 'Asia/Jakarta',
    
    // Upload settings
    'upload' => [
        'max_file_size' => 50 * 1024 * 1024, // 50MB
        'max_duration' => 30, // 30 seconds
        'allowed_types' => ['mp4', 'mov', 'avi', 'webm'],
        'upload_path' => 'uploads/videos/',
        'thumbnail_path' => 'uploads/thumbnails/',
    ],
    
    // Session settings
    'session' => [
        'lifetime' => 7200, // 2 hours
        'name' => 'storygue_session',
        'secure' => false, // Set to true in production with HTTPS
        'httponly' => true,
    ],
    
    // Security
    'csrf_token_name' => 'csrf_token',
    'password_hash_algo' => PASSWORD_DEFAULT,
];
