<?php

class Auth
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function register($username, $email, $password)
    {
        // Validate input
        $errors = [];
        
        if (empty($username) || strlen($username) < 3) {
            $errors['username'] = 'Username must be at least 3 characters';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Valid email is required';
        }
        
        if (empty($password) || strlen($password) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }
        
        // Check if username or email already exists
        $existingUser = $this->db->fetch(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            [$username, $email]
        );
        
        if ($existingUser) {
            $errors['general'] = 'Username or email already exists';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Create user
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $userId = $this->db->insert('users', [
            'username' => $username,
            'email' => $email,
            'password' => $hashedPassword
        ]);
        
        return ['success' => true, 'user_id' => $userId];
    }
    
    public function login($username, $password)
    {
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE username = ? OR email = ?",
            [$username, $username]
        );
        
        if (!$user || !password_verify($password, $user['password'])) {
            return ['success' => false, 'error' => 'Invalid credentials'];
        }
        
        // Create session
        $this->createSession($user);
        
        return ['success' => true, 'user' => $user];
    }
    
    public function logout()
    {
        if (isset($_SESSION['user_id'])) {
            // Delete session from database
            $this->db->delete('user_sessions', 'user_id = ?', [$_SESSION['user_id']]);
        }
        
        // Clear session
        session_destroy();
        session_start();
        
        return true;
    }
    
    public function user()
    {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE id = ?",
            [$_SESSION['user_id']]
        );
        
        return $user ?: null;
    }
    
    public function check()
    {
        return isset($_SESSION['user_id']) && $this->user() !== null;
    }
    
    public function guest()
    {
        return !$this->check();
    }
    
    public function isAdmin()
    {
        $user = $this->user();
        return $user && $user['is_admin'];
    }
    
    private function createSession($user)
    {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = $user['is_admin'];
        
        // Store session in database
        $sessionId = session_id();
        $expiresAt = date('Y-m-d H:i:s', time() + config('session.lifetime'));
        
        $this->db->query(
            "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at) 
             VALUES (?, ?, ?, ?, ?) 
             ON DUPLICATE KEY UPDATE 
             ip_address = VALUES(ip_address), 
             user_agent = VALUES(user_agent), 
             expires_at = VALUES(expires_at)",
            [
                $sessionId,
                $user['id'],
                getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $expiresAt
            ]
        );
    }
    
    public function requireAuth()
    {
        if ($this->guest()) {
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            redirect('/login');
        }
    }
    
    public function requireAdmin()
    {
        $this->requireAuth();
        
        if (!$this->isAdmin()) {
            redirect('/');
        }
    }
    
    public function requireGuest()
    {
        if ($this->check()) {
            redirect('/');
        }
    }
}
