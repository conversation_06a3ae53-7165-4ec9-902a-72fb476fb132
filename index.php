<?php
require_once 'bootstrap.php';

$video = new Video();
$auth = new Auth();

// Get videos with pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

$videos = $video->getAll($limit, $offset);
$stats = $video->getStats();

$pageTitle = 'StoryGue - Share Your Stories';
?>

<?php include 'includes/header.php'; ?>

<!-- Hero Section -->
<div class="bg-gradient-to-r from-black to-gray-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-4">
            Share Your <span class="text-white">Stories</span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-300 mb-8">
            Discover trending videos, viral content, and amazing stories from creators worldwide
        </p>
        
        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div class="bg-white/10 rounded-lg p-4">
                <div class="text-2xl font-bold"><?= number_format($stats['total_videos']) ?></div>
                <div class="text-sm text-gray-300">Videos</div>
            </div>
            <div class="bg-white/10 rounded-lg p-4">
                <div class="text-2xl font-bold"><?= number_format($stats['total_likes']) ?></div>
                <div class="text-sm text-gray-300">Likes</div>
            </div>
            <div class="bg-white/10 rounded-lg p-4">
                <div class="text-2xl font-bold"><?= number_format($stats['total_downloads']) ?></div>
                <div class="text-sm text-gray-300">Downloads</div>
            </div>
            <div class="bg-white/10 rounded-lg p-4">
                <div class="text-2xl font-bold"><?= number_format($stats['total_users']) ?></div>
                <div class="text-sm text-gray-300">Users</div>
            </div>
        </div>
        
        <?php if (!$auth->check()): ?>
        <div class="mt-8">
            <a href="/auth/register.php" class="bg-white text-black px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200 mr-4">
                Get Started
            </a>
            <a href="/upload.php" class="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-black transition duration-200">
                Upload Video
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex space-x-8">
            <a href="/" class="border-b-2 border-black text-black py-4 px-1 font-medium">
                <i class="fi fi-rr-home mr-1"></i> Latest
            </a>
            <a href="/trending.php" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 font-medium">
                <i class="fi fi-rr-flame mr-1"></i> Trending
            </a>
            <a href="/viral.php" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 font-medium">
                <i class="fi fi-rr-star mr-1"></i> Viral
            </a>
        </nav>
    </div>
</div>

<!-- Main Content -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <?php if (empty($videos)): ?>
        <!-- Empty State -->
        <div class="text-center py-16">
            <i class="fi fi-rr-video-camera text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No videos yet</h3>
            <p class="text-gray-600 mb-6">Be the first to share your story!</p>
            <?php if ($auth->check()): ?>
                <a href="/upload.php" class="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-200">
                    Upload First Video
                </a>
            <?php else: ?>
                <a href="/auth/register.php" class="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-200">
                    Join StoryGue
                </a>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <!-- Video Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <?php foreach ($videos as $videoItem): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden video-hover" data-video-id="<?= $videoItem['id'] ?>">
                    <!-- Video Thumbnail -->
                    <div class="relative aspect-video bg-gray-200">
                        <?php if ($videoItem['thumbnail']): ?>
                            <img src="/<?= htmlspecialchars($videoItem['thumbnail']) ?>" 
                                 alt="<?= htmlspecialchars($videoItem['title']) ?>"
                                 class="w-full h-full object-cover">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fi fi-rr-video-camera text-4xl text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Play Button Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-200">
                            <button class="bg-white/90 rounded-full p-3 hover:bg-white transition duration-200">
                                <i class="fi fi-rr-play text-2xl text-black"></i>
                            </button>
                        </div>
                        
                        <!-- Duration Badge -->
                        <?php if ($videoItem['duration']): ?>
                            <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                <?= gmdate("i:s", $videoItem['duration']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Video Info -->
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                            <?= htmlspecialchars($videoItem['title']) ?>
                        </h3>
                        
                        <div class="flex items-center text-sm text-gray-600 mb-3">
                            <i class="fi fi-rr-user mr-1"></i>
                            <span><?= htmlspecialchars($videoItem['username']) ?></span>
                            <span class="mx-2">•</span>
                            <span><?= timeAgo($videoItem['created_at']) ?></span>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <!-- Like Button -->
                                <button onclick="toggleLike(<?= $videoItem['id'] ?>, this)" 
                                        class="like-btn flex items-center text-gray-600 hover:text-red-500 transition duration-200">
                                    <i class="fi fi-rr-heart mr-1"></i>
                                    <span class="like-count"><?= number_format($videoItem['likes_count']) ?></span>
                                </button>
                                
                                <!-- Download Count -->
                                <div class="flex items-center text-gray-600">
                                    <i class="fi fi-rr-download mr-1"></i>
                                    <span class="download-count"><?= number_format($videoItem['downloads_count']) ?></span>
                                </div>
                            </div>
                            
                            <!-- Download Button -->
                            <button onclick="downloadVideo(<?= $videoItem['id'] ?>, '<?= htmlspecialchars($videoItem['filename']) ?>')"
                                    class="download-btn bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition duration-200 text-sm">
                                <i class="fi fi-rr-download mr-1"></i>
                                Download
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if (count($videos) === $limit): ?>
            <div class="mt-12 flex justify-center">
                <div class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>" 
                           class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Previous
                        </a>
                    <?php endif; ?>
                    
                    <span class="px-4 py-2 bg-black text-white rounded-md">
                        Page <?= $page ?>
                    </span>
                    
                    <a href="?page=<?= $page + 1 ?>" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Next
                    </a>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</main>

<?php include 'includes/footer.php'; ?>
