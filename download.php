<?php
require_once 'bootstrap.php';

$videoId = intval($_GET['id'] ?? 0);
if (!$videoId) {
    http_response_code(400);
    die('Invalid video ID');
}

$video = new Video();
$videoData = $video->getById($videoId);

if (!$videoData) {
    http_response_code(404);
    die('Video not found');
}

$videoPath = config('upload.upload_path') . $videoData['filename'];
if (!file_exists($videoPath)) {
    http_response_code(404);
    die('Video file not found');
}

// Set headers for download
$filename = $videoData['title'] . '_' . $videoData['id'] . '.' . pathinfo($videoData['filename'], PATHINFO_EXTENSION);
$filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);

header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($videoPath));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Output file
readfile($videoPath);
exit;
