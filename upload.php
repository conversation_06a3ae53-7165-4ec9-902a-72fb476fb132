<?php
require_once 'bootstrap.php';

$auth = new Auth();
$auth->requireAuth();

$user = $auth->user();
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors['general'] = 'Invalid security token';
    } else {
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        
        $videoUpload = new VideoUpload();
        $result = $videoUpload->upload($_FILES['video'] ?? [], $title, $description, $user['id']);
        
        if ($result['success']) {
            $success = true;
            $_SESSION['upload_success'] = 'Video uploaded successfully!';
        } else {
            $errors = $result['errors'];
        }
    }
}

$pageTitle = 'Upload Video - StoryGue';
?>

<?php include 'includes/header.php'; ?>

<!-- Header -->
<div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-3xl md:text-5xl font-bold mb-4">
            <i class="fi fi-rr-cloud-upload mr-2"></i>
            Upload Your Story
        </h1>
        <p class="text-xl text-blue-100">
            Share your amazing videos with the world
        </p>
    </div>
</div>

<!-- Main Content -->
<main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Success Message -->
    <?php if ($success): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fi fi-rr-check-circle mr-2"></i>
                <span>Video uploaded successfully! <a href="/" class="font-medium underline">View all videos</a></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside space-y-1">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Upload Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="POST" enctype="multipart/form-data" class="space-y-6" id="uploadForm">
            <?= csrf_field() ?>
            
            <!-- Video File Upload -->
            <div>
                <label for="video" class="block text-sm font-medium text-gray-700 mb-2">
                    Video File *
                </label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition duration-200" id="dropZone">
                    <input type="file" id="video" name="video" accept="video/*" class="hidden" required>
                    <div id="dropZoneContent">
                        <i class="fi fi-rr-cloud-upload text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-900 mb-2">Drop your video here or click to browse</p>
                        <p class="text-sm text-gray-500 mb-4">
                            Maximum file size: <?= formatFileSize(config('upload.max_file_size')) ?><br>
                            Maximum duration: <?= config('upload.max_duration') ?> seconds<br>
                            Supported formats: <?= implode(', ', config('upload.allowed_types')) ?>
                        </p>
                        <button type="button" class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition duration-200" onclick="document.getElementById('video').click()">
                            Choose File
                        </button>
                    </div>
                    <div id="filePreview" class="hidden">
                        <div class="flex items-center justify-center space-x-4">
                            <i class="fi fi-rr-video-camera text-2xl text-blue-500"></i>
                            <div class="text-left">
                                <p class="font-medium text-gray-900" id="fileName"></p>
                                <p class="text-sm text-gray-500" id="fileSize"></p>
                            </div>
                            <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile()">
                                <i class="fi fi-rr-cross-circle text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                </label>
                <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    value="<?= htmlspecialchars($_POST['title'] ?? '') ?>"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter a catchy title for your video"
                    maxlength="255"
                    required
                >
                <p class="text-sm text-gray-500 mt-1">
                    <span id="titleCount">0</span>/255 characters
                </p>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description (Optional)
                </label>
                <textarea 
                    id="description" 
                    name="description" 
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell us about your video..."
                    maxlength="1000"
                ><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                <p class="text-sm text-gray-500 mt-1">
                    <span id="descCount">0</span>/1000 characters
                </p>
            </div>

            <!-- Upload Progress -->
            <div id="uploadProgress" class="hidden">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%" id="progressBar"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2 text-center">Uploading... <span id="progressText">0%</span></p>
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-between">
                <a href="/" class="text-gray-500 hover:text-gray-700 transition duration-200">
                    ← Back to Home
                </a>
                <button 
                    type="submit" 
                    class="bg-blue-500 text-white px-8 py-3 rounded-md hover:bg-blue-600 transition duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    id="submitBtn"
                >
                    <i class="fi fi-rr-cloud-upload mr-2"></i>
                    Upload Video
                </button>
            </div>
        </form>
    </div>

    <!-- Upload Tips -->
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-4">
            <i class="fi fi-rr-bulb mr-2"></i>
            Upload Tips
        </h3>
        <ul class="space-y-2 text-blue-800">
            <li class="flex items-start">
                <i class="fi fi-rr-check text-blue-500 mr-2 mt-1"></i>
                <span>Keep your videos under <?= config('upload.max_duration') ?> seconds for better engagement</span>
            </li>
            <li class="flex items-start">
                <i class="fi fi-rr-check text-blue-500 mr-2 mt-1"></i>
                <span>Use descriptive titles to help people find your content</span>
            </li>
            <li class="flex items-start">
                <i class="fi fi-rr-check text-blue-500 mr-2 mt-1"></i>
                <span>High-quality videos get more likes and downloads</span>
            </li>
            <li class="flex items-start">
                <i class="fi fi-rr-check text-blue-500 mr-2 mt-1"></i>
                <span>Add relevant descriptions to provide context</span>
            </li>
        </ul>
    </div>
</main>

<script>
// Character counters
document.getElementById('title').addEventListener('input', function() {
    document.getElementById('titleCount').textContent = this.value.length;
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('descCount').textContent = this.value.length;
});

// File upload handling
const dropZone = document.getElementById('dropZone');
const fileInput = document.getElementById('video');
const dropZoneContent = document.getElementById('dropZoneContent');
const filePreview = document.getElementById('filePreview');

// Drag and drop
dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.classList.add('border-blue-400', 'bg-blue-50');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.classList.remove('border-blue-400', 'bg-blue-50');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    this.classList.remove('border-blue-400', 'bg-blue-50');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        showFilePreview(files[0]);
    }
});

// File input change
fileInput.addEventListener('change', function() {
    if (this.files.length > 0) {
        showFilePreview(this.files[0]);
    }
});

function showFilePreview(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    dropZoneContent.classList.add('hidden');
    filePreview.classList.remove('hidden');
}

function removeFile() {
    fileInput.value = '';
    dropZoneContent.classList.remove('hidden');
    filePreview.classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission with progress
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const progressDiv = document.getElementById('uploadProgress');
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fi fi-rr-spinner animate-spin mr-2"></i>Uploading...';
    progressDiv.classList.remove('hidden');
    
    // Simulate progress (in real implementation, you'd use XMLHttpRequest for actual progress)
    let progress = 0;
    const interval = setInterval(function() {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        document.getElementById('progressBar').style.width = progress + '%';
        document.getElementById('progressText').textContent = Math.round(progress) + '%';
        
        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 200);
});

// Initialize character counters
document.getElementById('titleCount').textContent = document.getElementById('title').value.length;
document.getElementById('descCount').textContent = document.getElementById('description').value.length;
</script>

<?php include 'includes/footer.php'; ?>
