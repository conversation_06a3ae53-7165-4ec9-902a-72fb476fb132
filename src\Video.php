<?php

class Video
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function create($data)
    {
        return $this->db->insert('videos', $data);
    }
    
    public function getAll($limit = 20, $offset = 0)
    {
        $sql = "
            SELECT v.*, u.username 
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.is_active = 1 
            ORDER BY v.created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        return $this->db->fetchAll($sql, [$limit, $offset]);
    }
    
    public function getById($id)
    {
        $sql = "
            SELECT v.*, u.username 
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.id = ? AND v.is_active = 1
        ";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    public function getByUserId($userId, $limit = 20, $offset = 0)
    {
        $sql = "
            SELECT v.*, u.username 
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.user_id = ? AND v.is_active = 1 
            ORDER BY v.created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        return $this->db->fetchAll($sql, [$userId, $limit, $offset]);
    }
    
    public function getTrending($limit = 20)
    {
        $sql = "
            SELECT v.*, u.username,
                   (v.likes_count * 2 + v.downloads_count) as popularity_score
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.is_active = 1 
                AND v.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ORDER BY popularity_score DESC, v.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function getViral($limit = 20)
    {
        $sql = "
            SELECT v.*, u.username 
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.is_active = 1 
                AND (v.likes_count >= 100 OR v.downloads_count >= 500)
            ORDER BY (v.likes_count + v.downloads_count) DESC, v.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function search($query, $limit = 20, $offset = 0)
    {
        $sql = "
            SELECT v.*, u.username 
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.is_active = 1 
                AND (v.title LIKE ? OR v.description LIKE ? OR u.username LIKE ?)
            ORDER BY v.created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $limit, $offset]);
    }
    
    public function update($id, $data)
    {
        return $this->db->update('videos', $data, 'id = ?', [$id]);
    }
    
    public function delete($id)
    {
        return $this->db->update('videos', ['is_active' => 0], 'id = ?', [$id]);
    }
    
    public function incrementLikes($id)
    {
        $sql = "UPDATE videos SET likes_count = likes_count + 1 WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    public function decrementLikes($id)
    {
        $sql = "UPDATE videos SET likes_count = GREATEST(likes_count - 1, 0) WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    public function incrementDownloads($id)
    {
        $sql = "UPDATE videos SET downloads_count = downloads_count + 1 WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    public function hasUserLiked($videoId, $ipAddress)
    {
        $sql = "SELECT id FROM video_likes WHERE video_id = ? AND ip_address = ?";
        return $this->db->fetch($sql, [$videoId, $ipAddress]) !== false;
    }
    
    public function addLike($videoId, $ipAddress)
    {
        try {
            $this->db->beginTransaction();
            
            // Insert like record
            $this->db->insert('video_likes', [
                'video_id' => $videoId,
                'ip_address' => $ipAddress
            ]);
            
            // Increment likes count
            $this->incrementLikes($videoId);
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    public function removeLike($videoId, $ipAddress)
    {
        try {
            $this->db->beginTransaction();
            
            // Delete like record
            $this->db->delete('video_likes', 'video_id = ? AND ip_address = ?', [$videoId, $ipAddress]);
            
            // Decrement likes count
            $this->decrementLikes($videoId);
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    public function addDownload($videoId, $ipAddress, $userAgent = '')
    {
        try {
            $this->db->beginTransaction();
            
            // Insert download record
            $this->db->insert('video_downloads', [
                'video_id' => $videoId,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent
            ]);
            
            // Increment downloads count
            $this->incrementDownloads($videoId);
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    public function getStats()
    {
        $totalVideos = $this->db->fetch("SELECT COUNT(*) as count FROM videos WHERE is_active = 1")['count'];
        $totalLikes = $this->db->fetch("SELECT SUM(likes_count) as count FROM videos WHERE is_active = 1")['count'] ?? 0;
        $totalDownloads = $this->db->fetch("SELECT SUM(downloads_count) as count FROM videos WHERE is_active = 1")['count'] ?? 0;
        $totalUsers = $this->db->fetch("SELECT COUNT(*) as count FROM users")['count'];
        
        return [
            'total_videos' => $totalVideos,
            'total_likes' => $totalLikes,
            'total_downloads' => $totalDownloads,
            'total_users' => $totalUsers
        ];
    }
}
