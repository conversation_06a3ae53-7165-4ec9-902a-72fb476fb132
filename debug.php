<?php
// Debug file to identify issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>StoryGue Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Information</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Check file permissions
echo "<h2>File Permissions</h2>";
$files_to_check = [
    'bootstrap.php',
    '.env',
    'src/Database.php',
    'src/Auth.php',
    'src/Video.php',
    'uploads/',
    'uploads/videos/',
    'uploads/thumbnails/'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "$file: " . substr(sprintf('%o', $perms), -4) . " ✓<br>";
    } else {
        echo "$file: NOT FOUND ✗<br>";
    }
}

// Test bootstrap loading step by step
echo "<h2>Bootstrap Loading Test</h2>";

try {
    echo "1. Starting session... ";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✓<br>";
    
    echo "2. Loading .env file... ";
    if (file_exists(__DIR__ . '/.env')) {
        $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value);
            }
        }
        echo "✓<br>";
    } else {
        echo "✗ .env file not found<br>";
    }
    
    echo "3. Setting up autoloader... ";
    spl_autoload_register(function ($class) {
        $file = __DIR__ . '/src/' . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
        return false;
    });
    echo "✓<br>";
    
    echo "4. Testing Database class... ";
    if (class_exists('Database')) {
        echo "✓ Class exists<br>";
        
        echo "5. Testing database connection... ";
        $db = Database::getInstance();
        echo "✓<br>";
        
    } else {
        echo "✗ Database class not found<br>";
    }
    
    echo "6. Testing Auth class... ";
    if (class_exists('Auth')) {
        echo "✓<br>";
    } else {
        echo "✗ Auth class not found<br>";
    }
    
    echo "7. Testing Video class... ";
    if (class_exists('Video')) {
        echo "✓<br>";
    } else {
        echo "✗ Video class not found<br>";
    }
    
    echo "<h3>All tests passed! ✓</h3>";
    echo "<a href='index.php'>Try accessing main page</a>";
    
} catch (Exception $e) {
    echo "<h3>Error occurred:</h3>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Show environment variables
echo "<h2>Environment Variables</h2>";
foreach ($_ENV as $key => $value) {
    if (strpos($key, 'DB_') === 0 || strpos($key, 'APP_') === 0) {
        echo "$key = " . (strpos($key, 'PASSWORD') !== false ? '***' : $value) . "<br>";
    }
}
?>
