<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Setup</h2>";

try {
    // Connect to MySQL
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS storygue");
    echo "✓ Database created/verified<br>";
    
    // Connect to storygue database
    $pdo = new PDO('mysql:host=localhost;dbname=storygue', 'root', '');
    
    // Read and execute SQL file
    $sql = file_get_contents('database.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Ignore table already exists errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "Warning: " . $e->getMessage() . "<br>";
                }
            }
        }
    }
    
    echo "✓ Database setup completed<br>";
    echo "<a href='index.php'>Go to StoryGue</a><br>";
    echo "<a href='db_test.php'>Test Database Again</a><br>";
    
} catch (PDOException $e) {
    echo "✗ Error: " . $e->getMessage() . "<br>";
}
?>
