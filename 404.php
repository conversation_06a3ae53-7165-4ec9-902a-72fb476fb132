<?php
require_once 'bootstrap.php';

http_response_code(404);
$pageTitle = '404 - Page Not Found - StoryGue';
?>

<?php include 'includes/header.php'; ?>

<!-- 404 Error Page -->
<main class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Logo -->
        <div>
            <h1 class="text-4xl font-bold text-black mb-2">
                <span class="bg-black text-white px-3 py-2 rounded-lg">Sg</span>
                <span class="text-black ml-2">StoryGue</span>
            </h1>
        </div>

        <!-- 404 Illustration -->
        <div class="py-8">
            <div class="text-9xl font-bold text-gray-300 mb-4">404</div>
            <i class="fi fi-rr-video-camera text-6xl text-gray-400 mb-6"></i>
        </div>

        <!-- Error Message -->
        <div class="space-y-4">
            <h2 class="text-3xl font-bold text-gray-900">
                Oops! Page Not Found
            </h2>
            <p class="text-lg text-gray-600">
                The page you're looking for doesn't exist or has been moved.
            </p>
            <p class="text-sm text-gray-500">
                Don't worry, let's get you back to discovering amazing stories!
            </p>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/" class="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-200 font-medium">
                    <i class="fi fi-rr-home mr-2"></i>
                    Go Home
                </a>
                <a href="/trending" class="border border-black text-black px-6 py-3 rounded-lg hover:bg-black hover:text-white transition duration-200 font-medium">
                    <i class="fi fi-rr-flame mr-2"></i>
                    View Trending
                </a>
            </div>
            
            <div class="pt-4">
                <a href="javascript:history.back()" class="text-gray-500 hover:text-gray-700 transition duration-200">
                    ← Go Back
                </a>
            </div>
        </div>

        <!-- Search -->
        <div class="pt-8">
            <form action="/" method="GET" class="max-w-sm mx-auto">
                <div class="relative">
                    <input 
                        type="text" 
                        name="search" 
                        placeholder="Search for videos..."
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    >
                    <button type="submit" class="absolute right-2 top-2 text-gray-400 hover:text-black">
                        <i class="fi fi-rr-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Popular Links -->
        <div class="pt-8 border-t border-gray-200">
            <h3 class="text-sm font-medium text-gray-900 mb-4">Popular Pages</h3>
            <div class="grid grid-cols-2 gap-2 text-sm">
                <a href="/" class="text-gray-600 hover:text-black transition duration-200">Latest Videos</a>
                <a href="/viral" class="text-gray-600 hover:text-black transition duration-200">Viral Content</a>
                <a href="/upload" class="text-gray-600 hover:text-black transition duration-200">Upload Video</a>
                <a href="/terms" class="text-gray-600 hover:text-black transition duration-200">Terms of Service</a>
            </div>
        </div>
    </div>
</main>

<?php include 'includes/footer.php'; ?>
