<?php

class VideoUpload
{
    private $db;
    private $uploadPath;
    private $thumbnailPath;
    private $maxFileSize;
    private $maxDuration;
    private $allowedTypes;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->uploadPath = config('upload.upload_path');
        $this->thumbnailPath = config('upload.thumbnail_path');
        $this->maxFileSize = config('upload.max_file_size');
        $this->maxDuration = config('upload.max_duration');
        $this->allowedTypes = config('upload.allowed_types');
        
        // Create directories if they don't exist
        if (!is_dir($this->uploadPath)) {
            mkdir($this->uploadPath, 0755, true);
        }
        if (!is_dir($this->thumbnailPath)) {
            mkdir($this->thumbnailPath, 0755, true);
        }
    }
    
    public function upload($file, $title, $description, $userId)
    {
        $errors = [];
        
        // Validate input
        if (empty($title) || strlen($title) < 3) {
            $errors['title'] = 'Title must be at least 3 characters';
        }
        
        if (strlen($title) > 255) {
            $errors['title'] = 'Title must be less than 255 characters';
        }
        
        if (strlen($description) > 1000) {
            $errors['description'] = 'Description must be less than 1000 characters';
        }
        
        // Validate file
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors['video'] = 'Please select a video file';
        } else {
            $fileErrors = $this->validateFile($file);
            $errors = array_merge($errors, $fileErrors);
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            // Generate unique filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $this->uploadPath . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                throw new Exception('Failed to upload file');
            }
            
            // Get video info
            $videoInfo = $this->getVideoInfo($filePath);
            
            // Generate thumbnail
            $thumbnailFilename = $this->generateThumbnail($filePath, $filename);
            
            // Save to database
            $videoId = $this->db->insert('videos', [
                'user_id' => $userId,
                'title' => $title,
                'description' => $description,
                'filename' => $filename,
                'thumbnail' => $thumbnailFilename,
                'file_size' => filesize($filePath),
                'duration' => $videoInfo['duration'] ?? null
            ]);
            
            return [
                'success' => true,
                'video_id' => $videoId,
                'filename' => $filename
            ];
            
        } catch (Exception $e) {
            // Clean up uploaded file if database insert fails
            if (isset($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
            if (isset($thumbnailFilename) && file_exists($this->thumbnailPath . $thumbnailFilename)) {
                unlink($this->thumbnailPath . $thumbnailFilename);
            }
            
            return [
                'success' => false,
                'errors' => ['general' => 'Upload failed: ' . $e->getMessage()]
            ];
        }
    }
    
    private function validateFile($file)
    {
        $errors = [];
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            $errors['video'] = 'File size must be less than ' . formatFileSize($this->maxFileSize);
        }
        
        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedTypes)) {
            $errors['video'] = 'File type not allowed. Allowed types: ' . implode(', ', $this->allowedTypes);
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'video/mp4',
            'video/quicktime',
            'video/x-msvideo',
            'video/webm'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            $errors['video'] = 'Invalid video file format';
        }
        
        return $errors;
    }
    
    private function getVideoInfo($filePath)
    {
        $info = [];
        
        // Try to get video duration using ffprobe if available
        if (function_exists('shell_exec')) {
            $command = "ffprobe -v quiet -show_entries format=duration -of csv=\"p=0\" " . escapeshellarg($filePath) . " 2>/dev/null";
            $duration = shell_exec($command);
            
            if ($duration !== null) {
                $duration = floatval(trim($duration));
                
                // Check if duration exceeds maximum
                if ($duration > $this->maxDuration) {
                    throw new Exception("Video duration ({$duration}s) exceeds maximum allowed ({$this->maxDuration}s)");
                }
                
                $info['duration'] = round($duration);
            }
        }
        
        return $info;
    }
    
    private function generateThumbnail($videoPath, $videoFilename)
    {
        $thumbnailFilename = pathinfo($videoFilename, PATHINFO_FILENAME) . '_thumb.jpg';
        $thumbnailPath = $this->thumbnailPath . $thumbnailFilename;
        
        // Try to generate thumbnail using ffmpeg if available
        if (function_exists('shell_exec')) {
            $command = "ffmpeg -i " . escapeshellarg($videoPath) . " -ss 00:00:01 -vframes 1 -y " . escapeshellarg($thumbnailPath) . " 2>/dev/null";
            $result = shell_exec($command);
            
            if (file_exists($thumbnailPath) && filesize($thumbnailPath) > 0) {
                return $this->thumbnailPath . $thumbnailFilename;
            }
        }
        
        // Fallback: create a placeholder thumbnail
        $this->createPlaceholderThumbnail($thumbnailPath);
        
        return $this->thumbnailPath . $thumbnailFilename;
    }
    
    private function createPlaceholderThumbnail($thumbnailPath)
    {
        $width = 320;
        $height = 180;
        
        $image = imagecreate($width, $height);
        $bgColor = imagecolorallocate($image, 240, 240, 240);
        $textColor = imagecolorallocate($image, 100, 100, 100);
        
        imagefill($image, 0, 0, $bgColor);
        
        $text = 'Video Thumbnail';
        $fontSize = 3;
        $textWidth = imagefontwidth($fontSize) * strlen($text);
        $textHeight = imagefontheight($fontSize);
        
        $x = ($width - $textWidth) / 2;
        $y = ($height - $textHeight) / 2;
        
        imagestring($image, $fontSize, $x, $y, $text, $textColor);
        
        imagejpeg($image, $thumbnailPath, 80);
        imagedestroy($image);
    }
    
    public function deleteVideo($videoId, $userId = null)
    {
        $video = new Video();
        $videoData = $video->getById($videoId);
        
        if (!$videoData) {
            return ['success' => false, 'error' => 'Video not found'];
        }
        
        // Check ownership if userId is provided
        if ($userId !== null && $videoData['user_id'] != $userId) {
            return ['success' => false, 'error' => 'Unauthorized'];
        }
        
        try {
            // Delete files
            $videoPath = $this->uploadPath . $videoData['filename'];
            if (file_exists($videoPath)) {
                unlink($videoPath);
            }
            
            if ($videoData['thumbnail']) {
                $thumbnailPath = $videoData['thumbnail'];
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
            }
            
            // Soft delete from database
            $video->delete($videoId);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Failed to delete video'];
        }
    }
}
