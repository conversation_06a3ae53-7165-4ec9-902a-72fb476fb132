<?php
require_once '../bootstrap.php';

$auth = new Auth();
$auth->requireAdmin();

$video = new Video();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && verify_csrf_token($_POST['csrf_token'] ?? '')) {
    $action = $_POST['action'] ?? '';
    $videoId = intval($_POST['video_id'] ?? 0);
    
    if ($action === 'delete' && $videoId) {
        $videoUpload = new VideoUpload();
        $result = $videoUpload->deleteVideo($videoId);
        
        if ($result['success']) {
            $_SESSION['admin_message'] = 'Video deleted successfully';
        } else {
            $_SESSION['admin_error'] = $result['error'];
        }
    }
    
    redirect('/admin/videos.php');
}

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Search
$search = trim($_GET['search'] ?? '');
if ($search) {
    $videos = $video->search($search, $limit, $offset);
} else {
    $videos = $video->getAll($limit, $offset);
}

$pageTitle = 'Manage Videos - Admin - StoryGue';
?>

<?php include '../includes/header.php'; ?>

<!-- Admin Header -->
<div class="bg-gradient-to-r from-gray-800 to-black text-white py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">
                    <i class="fi fi-rr-video-camera mr-2"></i>
                    Manage Videos
                </h1>
                <p class="text-gray-300 mt-2">View and moderate all videos on the platform</p>
            </div>
            <a href="/admin/" class="bg-white text-black px-4 py-2 rounded-lg hover:bg-gray-100 transition duration-200">
                ← Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Messages -->
    <?php if (isset($_SESSION['admin_message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 alert-auto-hide">
            <?= htmlspecialchars($_SESSION['admin_message']) ?>
        </div>
        <?php unset($_SESSION['admin_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['admin_error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 alert-auto-hide">
            <?= htmlspecialchars($_SESSION['admin_error']) ?>
        </div>
        <?php unset($_SESSION['admin_error']); ?>
    <?php endif; ?>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <form method="GET" class="flex items-center space-x-4">
            <div class="flex-1">
                <input 
                    type="text" 
                    name="search" 
                    value="<?= htmlspecialchars($search) ?>"
                    placeholder="Search videos by title, description, or username..."
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
            </div>
            <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition duration-200">
                <i class="fi fi-rr-search mr-2"></i>
                Search
            </button>
            <?php if ($search): ?>
                <a href="/admin/videos.php" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition duration-200">
                    Clear
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Videos Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <?php if (empty($videos)): ?>
            <div class="p-8 text-center">
                <i class="fi fi-rr-video-camera text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No videos found</h3>
                <p class="text-gray-600">
                    <?= $search ? 'Try adjusting your search terms.' : 'No videos have been uploaded yet.' ?>
                </p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Video
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                User
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stats
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Uploaded
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($videos as $videoItem): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-16 h-12">
                                            <?php if ($videoItem['thumbnail']): ?>
                                                <img src="/<?= htmlspecialchars($videoItem['thumbnail']) ?>" 
                                                     alt="Thumbnail"
                                                     class="w-16 h-12 object-cover rounded">
                                            <?php else: ?>
                                                <div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                                                    <i class="fi fi-rr-video-camera text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?= htmlspecialchars($videoItem['title']) ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?= formatFileSize($videoItem['file_size']) ?>
                                                <?php if ($videoItem['duration']): ?>
                                                    • <?= gmdate("i:s", $videoItem['duration']) ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($videoItem['username']) ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fi fi-rr-heart text-red-500 mr-1"></i>
                                            <?= number_format($videoItem['likes_count']) ?>
                                        </div>
                                        <div class="flex items-center mt-1">
                                            <i class="fi fi-rr-download text-blue-500 mr-1"></i>
                                            <?= number_format($videoItem['downloads_count']) ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= timeAgo($videoItem['created_at']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="/download.php?id=<?= $videoItem['id'] ?>" 
                                           class="text-blue-600 hover:text-blue-900"
                                           title="Download">
                                            <i class="fi fi-rr-download"></i>
                                        </a>
                                        <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this video?')">
                                            <?= csrf_field() ?>
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="video_id" value="<?= $videoItem['id'] ?>">
                                            <button type="submit" class="text-red-600 hover:text-red-900" title="Delete">
                                                <i class="fi fi-rr-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if (count($videos) === $limit): ?>
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <a href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing page <span class="font-medium"><?= $page ?></span>
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            Previous
                                        </a>
                                    <?php endif; ?>
                                    <a href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        Next
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</main>

<?php include '../includes/footer.php'; ?>
