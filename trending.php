<?php
require_once 'bootstrap.php';

$video = new Video();
$videos = $video->getTrending(20);

$pageTitle = 'Trending Videos - StoryGue';
?>

<?php include 'includes/header.php'; ?>

<!-- Header -->
<div class="bg-gradient-to-r from-orange-500 to-red-600 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-3xl md:text-5xl font-bold mb-4">
            <i class="fi fi-rr-flame mr-2"></i>
            Trending Videos
        </h1>
        <p class="text-xl text-orange-100">
            Discover what's hot and trending this week
        </p>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex space-x-8">
            <a href="/" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 font-medium">
                <i class="fi fi-rr-home mr-1"></i> Latest
            </a>
            <a href="/trending.php" class="border-b-2 border-orange-500 text-orange-600 py-4 px-1 font-medium">
                <i class="fi fi-rr-flame mr-1"></i> Trending
            </a>
            <a href="/viral.php" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 font-medium">
                <i class="fi fi-rr-star mr-1"></i> Viral
            </a>
        </nav>
    </div>
</div>

<!-- Main Content -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <?php if (empty($videos)): ?>
        <!-- Empty State -->
        <div class="text-center py-16">
            <i class="fi fi-rr-flame text-6xl text-orange-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No trending videos yet</h3>
            <p class="text-gray-600 mb-6">Videos need more engagement to appear in trending!</p>
            <a href="/" class="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition duration-200">
                Browse All Videos
            </a>
        </div>
    <?php else: ?>
        <!-- Video Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <?php foreach ($videos as $index => $videoItem): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden video-hover relative" data-video-id="<?= $videoItem['id'] ?>">
                    <!-- Trending Badge -->
                    <?php if ($index < 3): ?>
                        <div class="absolute top-2 left-2 z-10">
                            <span class="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                #<?= $index + 1 ?> Trending
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Video Thumbnail -->
                    <div class="relative aspect-video bg-gray-200">
                        <?php if ($videoItem['thumbnail']): ?>
                            <img src="/<?= htmlspecialchars($videoItem['thumbnail']) ?>" 
                                 alt="<?= htmlspecialchars($videoItem['title']) ?>"
                                 class="w-full h-full object-cover">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fi fi-rr-video-camera text-4xl text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Play Button Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-200">
                            <button class="bg-white/90 rounded-full p-3 hover:bg-white transition duration-200">
                                <i class="fi fi-rr-play text-2xl text-black"></i>
                            </button>
                        </div>
                        
                        <!-- Duration Badge -->
                        <?php if ($videoItem['duration']): ?>
                            <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                <?= gmdate("i:s", $videoItem['duration']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Video Info -->
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                            <?= htmlspecialchars($videoItem['title']) ?>
                        </h3>
                        
                        <div class="flex items-center text-sm text-gray-600 mb-3">
                            <i class="fi fi-rr-user mr-1"></i>
                            <span><?= htmlspecialchars($videoItem['username']) ?></span>
                            <span class="mx-2">•</span>
                            <span><?= timeAgo($videoItem['created_at']) ?></span>
                        </div>
                        
                        <!-- Popularity Score -->
                        <?php if (isset($videoItem['popularity_score'])): ?>
                            <div class="flex items-center text-xs text-orange-600 mb-3">
                                <i class="fi fi-rr-flame mr-1"></i>
                                <span>Popularity Score: <?= number_format($videoItem['popularity_score']) ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Action Buttons -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <!-- Like Button -->
                                <button onclick="toggleLike(<?= $videoItem['id'] ?>, this)" 
                                        class="like-btn flex items-center text-gray-600 hover:text-red-500 transition duration-200">
                                    <i class="fi fi-rr-heart mr-1"></i>
                                    <span class="like-count"><?= number_format($videoItem['likes_count']) ?></span>
                                </button>
                                
                                <!-- Download Count -->
                                <div class="flex items-center text-gray-600">
                                    <i class="fi fi-rr-download mr-1"></i>
                                    <span class="download-count"><?= number_format($videoItem['downloads_count']) ?></span>
                                </div>
                            </div>
                            
                            <!-- Download Button -->
                            <button onclick="downloadVideo(<?= $videoItem['id'] ?>, '<?= htmlspecialchars($videoItem['filename']) ?>')"
                                    class="download-btn bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-200 text-sm">
                                <i class="fi fi-rr-download mr-1"></i>
                                Download
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</main>

<?php include 'includes/footer.php'; ?>
