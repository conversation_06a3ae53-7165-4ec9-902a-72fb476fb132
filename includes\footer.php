    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="bg-black text-white px-3 py-2 rounded-lg font-bold text-xl">Sg</span>
                        <span class="text-black ml-2 font-bold text-xl">StoryGue</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Share your stories through short videos. Discover trending content and viral status updates from WhatsApp and CapCut.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-black transition duration-200">
                            <i class="fi fi-rr-share text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-black transition duration-200">
                            <i class="fi fi-rr-heart text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-black transition duration-200">
                            <i class="fi fi-rr-star text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold text-gray-900 mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-600 hover:text-black text-sm transition duration-200">Home</a></li>
                        <li><a href="/trending.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Trending</a></li>
                        <li><a href="/viral.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Viral</a></li>
                        <li><a href="/upload.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Upload</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 class="font-semibold text-gray-900 mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="/terms.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Terms of Service</a></li>
                        <li><a href="/privacy.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Privacy Policy</a></li>
                        <li><a href="/contact.php" class="text-gray-600 hover:text-black text-sm transition duration-200">Contact Us</a></li>
                        <li><a href="/about.php" class="text-gray-600 hover:text-black text-sm transition duration-200">About</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t pt-6 mt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-500 text-sm">
                        © <?= date('Y') ?> StoryGue. All rights reserved.
                    </p>
                    <p class="text-gray-500 text-sm mt-2 md:mt-0">
                        Made with <i class="fi fi-rr-heart text-red-500"></i> for content creators
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Like button functionality
        function toggleLike(videoId, button) {
            fetch('/api/like.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    csrf_token: '<?= csrf_token() ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const countElement = button.querySelector('.like-count');
                    const icon = button.querySelector('i');
                    
                    countElement.textContent = data.likes_count;
                    
                    if (data.liked) {
                        icon.classList.remove('fi-rr-heart');
                        icon.classList.add('fi-rr-heart', 'text-red-500');
                        button.classList.add('text-red-500');
                    } else {
                        icon.classList.remove('text-red-500');
                        button.classList.remove('text-red-500');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // Download functionality
        function downloadVideo(videoId, filename) {
            // Track download
            fetch('/api/download.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    csrf_token: '<?= csrf_token() ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update download count
                    const countElement = document.querySelector(`[data-video-id="${videoId}"] .download-count`);
                    if (countElement) {
                        countElement.textContent = data.downloads_count;
                    }
                    
                    // Start download
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-auto-hide');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Mobile optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on input focus for iOS
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                const inputs = document.querySelectorAll('input, textarea, select');
                inputs.forEach(function(input) {
                    if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                        input.style.fontSize = '16px';
                    }
                });
            }

            // Lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.removeAttribute('data-src');
                                observer.unobserve(img);
                            }
                        }
                    });
                });

                const lazyImages = document.querySelectorAll('img[data-src]');
                lazyImages.forEach(function(img) {
                    imageObserver.observe(img);
                });
            }

            // Performance optimization - debounce scroll events
            let ticking = false;
            function updateScrollPosition() {
                // Add scroll-based optimizations here if needed
                ticking = false;
            }

            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollPosition);
                    ticking = true;
                }
            });
        });
    </script>
</body>
</html>
