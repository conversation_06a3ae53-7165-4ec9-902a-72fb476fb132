<?php
require_once '../bootstrap.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!verify_csrf_token($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
    exit;
}

$videoId = intval($input['video_id'] ?? 0);
if (!$videoId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid video ID']);
    exit;
}

$video = new Video();
$ipAddress = getClientIP();
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// Check if video exists
$videoData = $video->getById($videoId);
if (!$videoData) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Video not found']);
    exit;
}

// Check if video file exists
$videoPath = config('upload.upload_path') . $videoData['filename'];
if (!file_exists($videoPath)) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Video file not found']);
    exit;
}

try {
    // Track download
    $video->addDownload($videoId, $ipAddress, $userAgent);
    
    // Get updated video data
    $updatedVideo = $video->getById($videoId);
    
    echo json_encode([
        'success' => true,
        'downloads_count' => intval($updatedVideo['downloads_count']),
        'download_url' => '/download.php?id=' . $videoId
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
