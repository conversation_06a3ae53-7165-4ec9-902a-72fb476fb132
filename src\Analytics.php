<?php

class Analytics
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function getOverallStats()
    {
        $stats = [];
        
        // Total videos
        $stats['total_videos'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM videos WHERE is_active = 1"
        )['count'];
        
        // Total users
        $stats['total_users'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM users"
        )['count'];
        
        // Total likes
        $stats['total_likes'] = $this->db->fetch(
            "SELECT SUM(likes_count) as count FROM videos WHERE is_active = 1"
        )['count'] ?? 0;
        
        // Total downloads
        $stats['total_downloads'] = $this->db->fetch(
            "SELECT SUM(downloads_count) as count FROM videos WHERE is_active = 1"
        )['count'] ?? 0;
        
        // Today's stats
        $stats['today_uploads'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM videos WHERE DATE(created_at) = CURDATE() AND is_active = 1"
        )['count'];
        
        $stats['today_downloads'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM video_downloads WHERE DATE(created_at) = CURDATE()"
        )['count'];
        
        $stats['today_likes'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM video_likes WHERE DATE(created_at) = CURDATE()"
        )['count'];
        
        return $stats;
    }
    
    public function getTopVideos($limit = 10)
    {
        $sql = "
            SELECT v.*, u.username,
                   (v.likes_count * 2 + v.downloads_count) as popularity_score
            FROM videos v 
            JOIN users u ON v.user_id = u.id 
            WHERE v.is_active = 1 
            ORDER BY popularity_score DESC, v.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function getTopUsers($limit = 10)
    {
        $sql = "
            SELECT u.*, 
                   COUNT(v.id) as video_count,
                   SUM(v.likes_count) as total_likes,
                   SUM(v.downloads_count) as total_downloads
            FROM users u 
            LEFT JOIN videos v ON u.id = v.user_id AND v.is_active = 1
            GROUP BY u.id 
            ORDER BY video_count DESC, total_likes DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function getDownloadsByDate($days = 30)
    {
        $sql = "
            SELECT DATE(created_at) as date, COUNT(*) as downloads
            FROM video_downloads 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        return $this->db->fetchAll($sql, [$days]);
    }
    
    public function getLikesByDate($days = 30)
    {
        $sql = "
            SELECT DATE(created_at) as date, COUNT(*) as likes
            FROM video_likes 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        return $this->db->fetchAll($sql, [$days]);
    }
    
    public function getUploadsByDate($days = 30)
    {
        $sql = "
            SELECT DATE(created_at) as date, COUNT(*) as uploads
            FROM videos 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND is_active = 1
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        return $this->db->fetchAll($sql, [$days]);
    }
    
    public function getTopCountries($limit = 10)
    {
        // This would require a GeoIP service to convert IP to country
        // For now, we'll just return top IP addresses
        $sql = "
            SELECT ip_address, COUNT(*) as download_count
            FROM video_downloads 
            GROUP BY ip_address 
            ORDER BY download_count DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function getVideoStats($videoId)
    {
        $stats = [];
        
        // Basic video info
        $video = $this->db->fetch(
            "SELECT * FROM videos WHERE id = ? AND is_active = 1",
            [$videoId]
        );
        
        if (!$video) {
            return null;
        }
        
        $stats['video'] = $video;
        
        // Download stats by date
        $stats['downloads_by_date'] = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as downloads
             FROM video_downloads 
             WHERE video_id = ?
             GROUP BY DATE(created_at)
             ORDER BY date ASC",
            [$videoId]
        );
        
        // Likes by date
        $stats['likes_by_date'] = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as likes
             FROM video_likes 
             WHERE video_id = ?
             GROUP BY DATE(created_at)
             ORDER BY date ASC",
            [$videoId]
        );
        
        // Top downloading IPs
        $stats['top_ips'] = $this->db->fetchAll(
            "SELECT ip_address, COUNT(*) as download_count
             FROM video_downloads 
             WHERE video_id = ?
             GROUP BY ip_address 
             ORDER BY download_count DESC
             LIMIT 10",
            [$videoId]
        );
        
        return $stats;
    }
    
    public function getUserStats($userId)
    {
        $stats = [];
        
        // User info
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );
        
        if (!$user) {
            return null;
        }
        
        $stats['user'] = $user;
        
        // Video count
        $stats['video_count'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM videos WHERE user_id = ? AND is_active = 1",
            [$userId]
        )['count'];
        
        // Total likes
        $stats['total_likes'] = $this->db->fetch(
            "SELECT SUM(likes_count) as count FROM videos WHERE user_id = ? AND is_active = 1",
            [$userId]
        )['count'] ?? 0;
        
        // Total downloads
        $stats['total_downloads'] = $this->db->fetch(
            "SELECT SUM(downloads_count) as count FROM videos WHERE user_id = ? AND is_active = 1",
            [$userId]
        )['count'] ?? 0;
        
        // Most popular video
        $stats['most_popular_video'] = $this->db->fetch(
            "SELECT *, (likes_count * 2 + downloads_count) as popularity_score
             FROM videos 
             WHERE user_id = ? AND is_active = 1
             ORDER BY popularity_score DESC
             LIMIT 1",
            [$userId]
        );
        
        // Recent activity
        $stats['recent_videos'] = $this->db->fetchAll(
            "SELECT * FROM videos 
             WHERE user_id = ? AND is_active = 1
             ORDER BY created_at DESC
             LIMIT 5",
            [$userId]
        );
        
        return $stats;
    }
    
    public function trackPageView($page, $ipAddress, $userAgent = '')
    {
        // Simple page view tracking
        $this->db->query(
            "INSERT INTO page_views (page, ip_address, user_agent, created_at) VALUES (?, ?, ?, NOW())",
            [$page, $ipAddress, $userAgent]
        );
    }
    
    public function getPopularPages($limit = 10)
    {
        $sql = "
            SELECT page, COUNT(*) as views
            FROM page_views 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY page 
            ORDER BY views DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
}
