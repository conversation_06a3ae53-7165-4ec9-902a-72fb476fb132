<?php
$auth = new Auth();
$user = $auth->user();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? 'StoryGue - Share Your Stories' ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <style>
        .video-hover:hover {
            transform: scale(1.02);
            transition: transform 0.2s ease-in-out;
        }
        
        .download-btn:hover {
            animation: bounce 0.5s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }
        
        .like-btn:hover {
            animation: pulse 0.5s ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/storygue/" class="flex items-center">
                        <span class="bg-black text-white px-3 py-2 rounded-lg font-bold text-xl">Sg</span>
                        <span class="text-black ml-2 font-bold text-xl">StoryGue</span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/storygue/" class="text-gray-700 hover:text-black transition duration-200">
                        <i class="fi fi-rr-home mr-1"></i> Home
                    </a>
                    
                    <?php if ($user): ?>
                        <a href="/storygue/upload.php" class="text-gray-700 hover:text-black transition duration-200">
                            <i class="fi fi-rr-cloud-upload mr-1"></i> Upload
                        </a>

                        <?php if ($user['is_admin']): ?>
                            <a href="/storygue/admin/" class="text-gray-700 hover:text-black transition duration-200">
                                <i class="fi fi-rr-settings mr-1"></i> Admin
                            </a>
                        <?php endif; ?>
                        
                        <div class="relative group">
                            <button class="flex items-center text-gray-700 hover:text-black transition duration-200">
                                <i class="fi fi-rr-user mr-1"></i>
                                <?= htmlspecialchars($user['username']) ?>
                                <i class="fi fi-rr-angle-down ml-1"></i>
                            </button>
                            
                            <!-- Dropdown -->
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <a href="/storygue/profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fi fi-rr-user mr-2"></i> Profile
                                </a>
                                <a href="/storygue/logout.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fi fi-rr-sign-out mr-2"></i> Logout
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="/storygue/login.php" class="text-gray-700 hover:text-black transition duration-200">
                            <i class="fi fi-rr-sign-in mr-1"></i> Login
                        </a>
                        <a href="/storygue/register.php" class="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition duration-200">
                            Register
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-black">
                        <i class="fi fi-rr-menu-burger text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block px-3 py-2 text-gray-700 hover:text-black">
                    <i class="fi fi-rr-home mr-2"></i> Home
                </a>
                
                <?php if ($user): ?>
                    <a href="/upload.php" class="block px-3 py-2 text-gray-700 hover:text-black">
                        <i class="fi fi-rr-cloud-upload mr-2"></i> Upload
                    </a>
                    
                    <?php if ($user['is_admin']): ?>
                        <a href="/admin/" class="block px-3 py-2 text-gray-700 hover:text-black">
                            <i class="fi fi-rr-settings mr-2"></i> Admin
                        </a>
                    <?php endif; ?>
                    
                    <a href="/profile.php" class="block px-3 py-2 text-gray-700 hover:text-black">
                        <i class="fi fi-rr-user mr-2"></i> Profile
                    </a>
                    <a href="/auth/logout.php" class="block px-3 py-2 text-gray-700 hover:text-black">
                        <i class="fi fi-rr-sign-out mr-2"></i> Logout
                    </a>
                <?php else: ?>
                    <a href="/storygue/login.php" class="block px-3 py-2 text-gray-700 hover:text-black">
                        <i class="fi fi-rr-sign-in mr-2"></i> Login
                    </a>
                    <a href="/storygue/register.php" class="block px-3 py-2 text-gray-700 hover:text-black">
                        Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
    </script>
