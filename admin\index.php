<?php
require_once '../bootstrap.php';

$auth = new Auth();
$auth->requireAdmin();

$analytics = new Analytics();
$video = new Video();

// Get overall statistics
$stats = $analytics->getOverallStats();
$topVideos = $analytics->getTopVideos(5);
$topUsers = $analytics->getTopUsers(5);

// Handle video deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_video') {
    if (verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $videoId = intval($_POST['video_id'] ?? 0);
        $videoUpload = new VideoUpload();
        $result = $videoUpload->deleteVideo($videoId);
        
        if ($result['success']) {
            $_SESSION['admin_message'] = 'Video deleted successfully';
        } else {
            $_SESSION['admin_error'] = $result['error'];
        }
    }
    redirect('/admin/');
}

$pageTitle = 'Admin Dashboard - StoryGue';
?>

<?php include '../includes/header.php'; ?>

<!-- Admin Header -->
<div class="bg-gradient-to-r from-gray-800 to-black text-white py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 class="text-3xl font-bold">
            <i class="fi fi-rr-settings mr-2"></i>
            Admin Dashboard
        </h1>
        <p class="text-gray-300 mt-2">Manage your StoryGue platform</p>
    </div>
</div>

<!-- Main Content -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Messages -->
    <?php if (isset($_SESSION['admin_message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 alert-auto-hide">
            <?= htmlspecialchars($_SESSION['admin_message']) ?>
        </div>
        <?php unset($_SESSION['admin_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['admin_error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 alert-auto-hide">
            <?= htmlspecialchars($_SESSION['admin_error']) ?>
        </div>
        <?php unset($_SESSION['admin_error']); ?>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fi fi-rr-video-camera text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Videos</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= number_format($stats['total_videos']) ?></p>
                    <p class="text-sm text-green-600">+<?= $stats['today_uploads'] ?> today</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fi fi-rr-users text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= number_format($stats['total_users']) ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fi fi-rr-heart text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Likes</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= number_format($stats['total_likes']) ?></p>
                    <p class="text-sm text-green-600">+<?= $stats['today_likes'] ?> today</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fi fi-rr-download text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Downloads</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= number_format($stats['total_downloads']) ?></p>
                    <p class="text-sm text-green-600">+<?= $stats['today_downloads'] ?> today</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Top Videos -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Top Videos</h3>
            </div>
            <div class="p-6">
                <?php if (empty($topVideos)): ?>
                    <p class="text-gray-500 text-center py-4">No videos yet</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($topVideos as $index => $videoItem): ?>
                            <div class="flex items-center space-x-4 p-3 border rounded-lg">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-medium text-gray-600">
                                        <?= $index + 1 ?>
                                    </span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        <?= htmlspecialchars($videoItem['title']) ?>
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        by <?= htmlspecialchars($videoItem['username']) ?>
                                    </p>
                                </div>
                                <div class="flex-shrink-0 text-right">
                                    <p class="text-sm font-medium text-gray-900">
                                        <?= number_format($videoItem['likes_count']) ?> likes
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?= number_format($videoItem['downloads_count']) ?> downloads
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this video?')">
                                        <?= csrf_field() ?>
                                        <input type="hidden" name="action" value="delete_video">
                                        <input type="hidden" name="video_id" value="<?= $videoItem['id'] ?>">
                                        <button type="submit" class="text-red-600 hover:text-red-800">
                                            <i class="fi fi-rr-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Top Users -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Top Users</h3>
            </div>
            <div class="p-6">
                <?php if (empty($topUsers)): ?>
                    <p class="text-gray-500 text-center py-4">No users yet</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($topUsers as $index => $userItem): ?>
                            <div class="flex items-center space-x-4 p-3 border rounded-lg">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-medium text-gray-600">
                                        <?= $index + 1 ?>
                                    </span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($userItem['username']) ?>
                                        <?php if ($userItem['is_admin']): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                                Admin
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?= htmlspecialchars($userItem['email']) ?>
                                    </p>
                                </div>
                                <div class="flex-shrink-0 text-right">
                                    <p class="text-sm font-medium text-gray-900">
                                        <?= number_format($userItem['video_count']) ?> videos
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?= number_format($userItem['total_likes'] ?? 0) ?> total likes
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/admin/videos.php" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                <i class="fi fi-rr-video-camera text-2xl text-blue-600 mr-3"></i>
                <div>
                    <p class="font-medium text-gray-900">Manage Videos</p>
                    <p class="text-sm text-gray-500">View and moderate all videos</p>
                </div>
            </a>
            
            <a href="/admin/users.php" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                <i class="fi fi-rr-users text-2xl text-green-600 mr-3"></i>
                <div>
                    <p class="font-medium text-gray-900">Manage Users</p>
                    <p class="text-sm text-gray-500">View and manage user accounts</p>
                </div>
            </a>
            
            <a href="/admin/analytics.php" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                <i class="fi fi-rr-chart-line text-2xl text-purple-600 mr-3"></i>
                <div>
                    <p class="font-medium text-gray-900">Analytics</p>
                    <p class="text-sm text-gray-500">View detailed statistics</p>
                </div>
            </a>
        </div>
    </div>
</main>

<?php include '../includes/footer.php'; ?>
