<?php
require_once '../bootstrap.php';

$auth = new Auth();
$auth->requireGuest();

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        $result = $auth->login($username, $password);
        
        if ($result['success']) {
            $redirectUrl = $_SESSION['intended_url'] ?? '/';
            unset($_SESSION['intended_url']);
            redirect($redirectUrl);
        } else {
            $error = $result['error'];
        }
    }
}

$pageTitle = 'Login - StoryGue';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/uicons-regular-rounded/css/uicons-regular-rounded.css">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <!-- Logo -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-black">
                <span class="bg-black text-white px-2 py-1 rounded">Sg</span>
                <span class="text-black ml-1">StoryGue</span>
            </h1>
            <p class="text-gray-600 mt-2">Login to your account</p>
        </div>

        <!-- Error Message -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form method="POST" class="space-y-4">
            <?= csrf_field() ?>
            
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                    Username or Email
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    required
                >
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                    Password
                </label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    required
                >
            </div>

            <button 
                type="submit" 
                class="w-full bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800 transition duration-200 font-medium"
            >
                Login
            </button>
        </form>

        <!-- Links -->
        <div class="mt-6 text-center space-y-2">
            <p class="text-sm text-gray-600">
                Don't have an account? 
                <a href="/auth/register.php" class="text-black font-medium hover:underline">Register here</a>
            </p>
            <p class="text-sm">
                <a href="/" class="text-gray-500 hover:text-black">← Back to Home</a>
            </p>
        </div>
    </div>
</body>
</html>
