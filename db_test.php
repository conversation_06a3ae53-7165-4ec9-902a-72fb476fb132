<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Connection Test</h2>";

try {
    // Test MySQL connection
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    echo "✓ MySQL connection successful<br>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'storygue'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Database 'storygue' exists<br>";
    } else {
        echo "✗ Database 'storygue' does not exist. Creating...<br>";
        $pdo->exec("CREATE DATABASE storygue");
        echo "✓ Database 'storygue' created<br>";
    }
    
    // Connect to storygue database
    $pdo = new PDO('mysql:host=localhost;dbname=storygue', 'root', '');
    echo "✓ Connected to storygue database<br>";
    
    // Check if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "✗ No tables found. Need to run database.sql<br>";
        echo "<a href='setup_db.php'>Click here to setup database</a><br>";
    } else {
        echo "✓ Found tables: " . implode(', ', $tables) . "<br>";
    }
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "<br>";
}
?>
