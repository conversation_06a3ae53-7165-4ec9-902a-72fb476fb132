<?php
// Simple test file to check for errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current directory: " . __DIR__ . "<br>";

// Test if bootstrap loads
try {
    require_once 'bootstrap.php';
    echo "Bootstrap loaded successfully<br>";
    
    // Test database connection
    $db = Database::getInstance();
    echo "Database connection successful<br>";
    
    // Test if uploads directory exists
    if (!is_dir('uploads')) {
        mkdir('uploads', 0755, true);
        echo "Created uploads directory<br>";
    }
    
    if (!is_dir('uploads/videos')) {
        mkdir('uploads/videos', 0755, true);
        echo "Created uploads/videos directory<br>";
    }
    
    if (!is_dir('uploads/thumbnails')) {
        mkdir('uploads/thumbnails', 0755, true);
        echo "Created uploads/thumbnails directory<br>";
    }
    
    echo "All checks passed!<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>
